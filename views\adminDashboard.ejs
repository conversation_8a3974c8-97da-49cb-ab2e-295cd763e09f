<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Painel Administrativo - Checkin Room</title>
  <link rel="stylesheet" href="/CSS/Dashboard/styles.css">
</head>
<body>
  <!-- Header do Dashboard -->
  <header class="dashboard-header">
    <h1>Painel Administrativo</h1>
    <p class="dashboard-subtitle">Gerencie e aprove as reservas pendentes</p>
    <a href="/home" class="btn-home">
      <span>🏠</span>
      <span>Home</span>
    </a>
  </header>

  <!-- Container Principal -->
  <div class="dashboard-container">
    <!-- Mensagem de Feedback -->
    <% if (mensagemSucesso) { %>
      <div class="message sucesso"><%= mensagemSucesso %></div>
    <% } %>

    <!-- <PERSON><PERSON> de Filtros -->
    <section class="filters-section">
      <div class="filters-header">
        <h2 class="filters-title">Filtros de Busca</h2>
        <button class="filter-toggle-btn" id="toggleFilters">
          <span class="icon">🔍</span>
          <span>Filtrar Reservas</span>
        </button>
      </div>

      <form class="filters-form" id="filtersForm">
        <div class="filter-group">
          <label for="filterTipo">Tipo de Usuário</label>
          <select id="filterTipo" name="tipo_usuario">
            <option value="">Todos</option>
            <option value="aluno">Aluno</option>
            <option value="professor">Professor</option>
            <option value="admin">Admin</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="filterData">Data da Reserva</label>
          <input type="date" id="filterData" name="data_reserva">
        </div>

        <div class="filter-group">
          <label for="filterHorarioInicio">Horário Início</label>
          <input type="time" id="filterHorarioInicio" name="horario_inicio">
        </div>

        <div class="filter-group">
          <label for="filterHorarioFim">Horário Fim</label>
          <input type="time" id="filterHorarioFim" name="horario_fim">
        </div>

        <div class="filter-group">
          <label for="filterSala">Nome da Sala</label>
          <input type="text" id="filterSala" name="sala_nome" placeholder="Digite o nome da sala">
        </div>

        <div class="filter-group">
          <label for="filterUsuario">Nome do Usuário</label>
          <input type="text" id="filterUsuario" name="usuario_nome" placeholder="Digite o nome do usuário">
        </div>

        <div class="filter-group">
          <label for="filterCapacidade">Capacidade Mínima</label>
          <input type="number" id="filterCapacidade" name="capacidade" min="1" placeholder="Ex: 10">
        </div>

        <div class="filters-actions">
          <button type="button" class="btn-filter btn-clear" id="clearFilters">Limpar</button>
          <button type="button" class="btn-filter btn-apply" id="applyFilters">Aplicar Filtros</button>
        </div>
      </form>
    </section>

    <!-- Seção da Tabela -->
    <section class="table-section">
      <div class="table-header">
        <h2 class="table-title">Reservas Pendentes</h2>
        <span class="table-count" id="reservasCount"><%= reservasPendentes.length %> reserva(s)</span>
      </div>

      <% if (reservasPendentes.length > 0) { %>
        <table class="reservas-table" id="reservasTable">
          <thead>
            <tr>
              <th>Sala</th>
              <th>Horários de Reserva</th>
              <th>Nome do Usuário</th>
              <th>Prioridade</th>
              <th>Aprovação</th>
            </tr>
          </thead>
          <tbody>
            <% reservasPendentes.forEach(reserva => { %>
              <tr data-tipo="<%= reserva.tipo_usuario %>"
                  data-data="<%= reserva.data_reserva %>"
                  data-horario-inicio="<%= reserva.horario_inicio %>"
                  data-horario-fim="<%= reserva.horario_fim %>"
                  data-sala="<%= reserva.sala_nome.toLowerCase() %>"
                  data-capacidade="<%= reserva.capacidade %>"
                  data-usuario="<%= reserva.usuario_nome.toLowerCase() %>"
                  data-status="<%= reserva.status_reserva || 'pendente' %>">
                <td>
                  <div class="sala-info">
                    <span class="sala-nome"><%= reserva.sala_nome %></span>
                    <span class="sala-capacidade"><%= reserva.capacidade %> pessoas</span>
                  </div>
                </td>
                <td>
                  <div class="horario-info">
                    <span class="horario-data"><%= new Date(reserva.data_reserva).toLocaleDateString('pt-BR') %></span>
                    <span class="horario-periodo"><%= reserva.horario_inicio.slice(0,5) %> às <%= reserva.horario_fim.slice(0,5) %></span>
                  </div>
                </td>
                <td><%= reserva.usuario_nome %></td>
                <td>
                  <% if (reserva.tipo_usuario === 'aluno') { %>
                    <span class="priority-badge aluno">Aluno</span>
                  <% } else if (reserva.tipo_usuario === 'professor') { %>
                    <span class="priority-badge professor">Professor</span>
                  <% } %>
                </td>
                <td>
                  <div class="approval-cell" data-reserva-id="<%= reserva.id_reserva %>">
                    <% if (reserva.status_reserva === 'aprovada') { %>
                      <span class="status-badge aprovada">Aprovada</span>
                    <% } else if (reserva.status_reserva === 'cancelada') { %>
                      <span class="status-badge cancelada">Cancelada</span>
                    <% } else { %>
                      <div class="actions-cell">
                        <button class="btn-action btn-aprovar" data-id="<%= reserva.id_reserva %>">
                          Aprovar
                        </button>
                        <button class="btn-action btn-cancelar" data-id="<%= reserva.id_reserva %>">
                          Cancelar
                        </button>
                      </div>
                    <% } %>
                  </div>
                </td>
              </tr>
            <% }) %>
          </tbody>
        </table>
      <% } else { %>
        <div class="empty-state">
          <div class="icon">📋</div>
          <h3>Nenhuma reserva pendente</h3>
          <p>Todas as reservas foram processadas ou não há novas solicitações.</p>
        </div>
      <% } %>
    </section>
  </div>

  <script>
    // ===== FUNCIONALIDADE DE FILTROS =====

    // Toggle dos filtros
    const toggleFiltersBtn = document.getElementById('toggleFilters');
    const filtersForm = document.getElementById('filtersForm');

    toggleFiltersBtn.addEventListener('click', () => {
      filtersForm.classList.toggle('active');
      const isActive = filtersForm.classList.contains('active');
      toggleFiltersBtn.innerHTML = isActive
        ? '<span class="icon">🔼</span><span>Ocultar Filtros</span>'
        : '<span class="icon">🔍</span><span>Filtrar Reservas</span>';
    });

    // Aplicar filtros
    const applyFiltersBtn = document.getElementById('applyFilters');
    const clearFiltersBtn = document.getElementById('clearFilters');
    const reservasTable = document.getElementById('reservasTable');
    const reservasCount = document.getElementById('reservasCount');

    function applyFilters() {
      const filters = {
        tipo: document.getElementById('filterTipo').value.toLowerCase().trim(),
        data: document.getElementById('filterData').value.trim(),
        horarioInicio: document.getElementById('filterHorarioInicio').value.trim(),
        horarioFim: document.getElementById('filterHorarioFim').value.trim(),
        sala: document.getElementById('filterSala').value.toLowerCase().trim(),
        usuario: document.getElementById('filterUsuario').value.toLowerCase().trim(),
        capacidade: parseInt(document.getElementById('filterCapacidade').value) || 0
      };

      const rows = reservasTable.querySelectorAll('tbody tr');
      let visibleCount = 0;

      rows.forEach(row => {
        let shouldShow = true;

        // Filtro por tipo de usuário (só aplica se tiver valor)
        if (filters.tipo && row.dataset.tipo !== filters.tipo) {
          shouldShow = false;
        }

        // Filtro por data (só aplica se tiver valor)
        if (filters.data) {
          const rowDate = new Date(row.dataset.data).toISOString().split('T')[0];
          if (rowDate !== filters.data) {
            shouldShow = false;
          }
        }

        // Filtro por horário de início (só aplica se tiver valor)
        if (filters.horarioInicio) {
          const rowHorario = row.dataset.horarioInicio.slice(0, 5);
          if (rowHorario < filters.horarioInicio) {
            shouldShow = false;
          }
        }

        // Filtro por horário de fim (só aplica se tiver valor)
        if (filters.horarioFim) {
          const rowHorario = row.dataset.horarioFim.slice(0, 5);
          if (rowHorario > filters.horarioFim) {
            shouldShow = false;
          }
        }

        // Filtro por nome da sala (só aplica se tiver valor)
        if (filters.sala && !row.dataset.sala.includes(filters.sala)) {
          shouldShow = false;
        }

        // Filtro por nome do usuário (só aplica se tiver valor)
        if (filters.usuario && !row.dataset.usuario.includes(filters.usuario)) {
          shouldShow = false;
        }

        // Filtro por capacidade mínima (só aplica se tiver valor > 0)
        if (filters.capacidade > 0) {
          const rowCapacidade = parseInt(row.dataset.capacidade);
          if (rowCapacidade < filters.capacidade) {
            shouldShow = false;
          }
        }

        // Mostrar/ocultar linha
        row.style.display = shouldShow ? '' : 'none';
        if (shouldShow) visibleCount++;
      });

      // Atualizar contador
      reservasCount.textContent = `${visibleCount} reserva(s)`;
    }

    function clearFilters() {
      // Limpar todos os campos
      document.getElementById('filterTipo').value = '';
      document.getElementById('filterData').value = '';
      document.getElementById('filterHorarioInicio').value = '';
      document.getElementById('filterHorarioFim').value = '';
      document.getElementById('filterSala').value = '';
      document.getElementById('filterUsuario').value = '';
      document.getElementById('filterCapacidade').value = '';

      // Mostrar todas as linhas
      const rows = reservasTable.querySelectorAll('tbody tr');
      rows.forEach(row => {
        row.style.display = '';
      });

      // Restaurar contador original
      reservasCount.textContent = `${rows.length} reserva(s)`;
    }

    // Event listeners para os botões de filtro
    applyFiltersBtn.addEventListener('click', applyFilters);
    clearFiltersBtn.addEventListener('click', clearFilters);

    // Aplicar filtros em tempo real quando campos mudam
    document.querySelectorAll('#filtersForm input, #filtersForm select').forEach(input => {
      input.addEventListener('change', applyFilters);
      input.addEventListener('input', applyFilters);
    });

    // ===== FUNCIONALIDADE EXISTENTE DE APROVAÇÃO/CANCELAMENTO =====

    // Função para atualizar status na interface
    function updateApprovalStatus(reservaId, status) {
      const approvalCell = document.querySelector(`[data-reserva-id="${reservaId}"]`);
      if (approvalCell) {
        const statusBadge = status === 'aprovada'
          ? '<span class="status-badge aprovada">Aprovada</span>'
          : '<span class="status-badge cancelada">Cancelada</span>';

        approvalCell.innerHTML = statusBadge;

        // Atualizar data-status da linha
        const row = approvalCell.closest('tr');
        if (row) {
          row.dataset.status = status;
        }
      }
    }

    // Aprovar reserva
    document.querySelectorAll('.btn-aprovar').forEach(button => {
      button.addEventListener('click', async () => {
        const id = button.getAttribute('data-id');

        // Confirmação antes de aprovar
        if (!confirm('Tem certeza que deseja aprovar esta reserva?')) {
          return;
        }

        try {
          button.disabled = true;
          button.textContent = 'Aprovando...';

          const response = await fetch(`/reservas/${id}/aprovar`, { method: 'PUT' });
          if (response.ok) {
            // Atualizar interface sem recarregar a página
            updateApprovalStatus(id, 'aprovada');
          } else {
            alert('Erro ao aprovar reserva.');
            button.disabled = false;
            button.textContent = 'Aprovar';
          }
        } catch (error) {
          console.error('Erro:', error);
          alert('Erro inesperado.');
          button.disabled = false;
          button.textContent = 'Aprovar';
        }
      });
    });

    // Cancelar reserva
    document.querySelectorAll('.btn-cancelar').forEach(button => {
      button.addEventListener('click', async () => {
        const id = button.getAttribute('data-id');

        // Confirmação antes de cancelar
        if (!confirm('Tem certeza que deseja cancelar esta reserva?')) {
          return;
        }

        try {
          button.disabled = true;
          button.textContent = 'Cancelando...';

          const response = await fetch(`/reservas/${id}/cancelar`, { method: 'PUT' });
          if (response.ok) {
            // Atualizar interface sem recarregar a página
            updateApprovalStatus(id, 'cancelada');
          } else {
            alert('Erro ao cancelar reserva.');
            button.disabled = false;
            button.textContent = 'Cancelar';
          }
        } catch (error) {
          console.error('Erro:', error);
          alert('Erro inesperado.');
          button.disabled = false;
          button.textContent = 'Cancelar';
        }
      });
    });

    // ===== MELHORIAS DE UX =====

    // Adicionar loading state visual
    function addLoadingState(button, originalText) {
      button.disabled = true;
      button.style.opacity = '0.7';
      button.textContent = 'Processando...';
    }

    function removeLoadingState(button, originalText) {
      button.disabled = false;
      button.style.opacity = '1';
      button.textContent = originalText;
    }

    // Inicialização
    document.addEventListener('DOMContentLoaded', () => {
      console.log('Dashboard carregado com sucesso!');

      // Se houver filtros na URL (para futuras implementações)
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.has('filter')) {
        filtersForm.classList.add('active');
      }
    });
  </script>
</body>
</html>
