/* ===== IMPORTA ESTILOS BASE DO LOGIN ===== */
/* <PERSON><PERSON>liza as variáveis CSS e estilos base */

/* ===== RESET E CONFIGURAÇÕES GLOBAIS ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* ===== VARIÁVEIS CSS (GUIA DE ESTILOS) ===== */
:root {
  /* Cores Primárias */
  --primary-color: #2e2640;
  --primary-hover: #40385c;

  /* Cores Secundárias */
  --secondary-color: #E84A4A;
  --success-color: #18A135;
  --student-color: #3118EF;
  --teacher-color: #8E6821;

  /* Cores Neutras */
  --neutral-dark: #3F3357;
  --neutral-darker: #261B38;
  --neutral-darkest: #120C1D;
  --neutral-light: #FFFFFF;
  --neutral-gray: #f0f2f5;
  --neutral-border: #ccc;
  --neutral-text: #333;
  --neutral-text-light: #555;

  /* Tipografia */
  --font-family: 'Arial', sans-serif;
  --font-size-base: 16px;
  --font-size-large: 1.8rem;
  --font-size-medium: 1.2rem;

  /* Espaçamentos */
  --spacing-xs: 0.3rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* Bordas */
  --border-radius: 8px;
  --border-radius-large: 12px;

  /* Sombras */
  --shadow-light: 0 0 8px rgba(0,0,0,0.1);
  --shadow-medium: 0 0 15px rgba(0,0,0,0.1);
}

/* ===== LAYOUT PRINCIPAL ===== */
body {
  background: var(--primary-color);
  font-family: var(--font-family);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
  position: relative;
}

/* ===== BOTÃO HOME ===== */
.btn-home {
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--primary-hover);
  color: var(--neutral-light);
  border: 2px solid var(--primary-hover);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.btn-home:hover {
  background: var(--neutral-light);
  color: var(--primary-color);
  transform: translateY(-1px);
}

/* ===== CONTAINER PRINCIPAL ===== */
.cadastro-container {
  display: flex;
  background: var(--neutral-light);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-medium);
  overflow: hidden;
  max-width: 950px;
  width: 100%;
  min-height: 600px;
}

/* ===== SEÇÃO DA IMAGEM ===== */
.cadastro-image {
  flex: 1;
  background: url('/assets/login-image.png') center/contain no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  position: relative;
}

.cadastro-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/assets/login-image.png') center/contain no-repeat;
  opacity: 0.8;
}

.cadastro-image-content {
  text-align: center;
  color: var(--neutral-light);
  z-index: 1;
  position: relative;
}

.cadastro-image h2 {
  font-size: var(--font-size-large);
  margin-bottom: var(--spacing-md);
  font-weight: bold;
}

.cadastro-image p {
  font-size: var(--font-size-medium);
  opacity: 0.9;
  line-height: 1.5;
}

/* ===== SEÇÃO DO FORMULÁRIO ===== */
.cadastro-form {
  flex: 1.2;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.cadastro-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.cadastro-header h1 {
  color: var(--primary-color);
  font-size: var(--font-size-large);
  margin-bottom: var(--spacing-sm);
  font-weight: bold;
}

.cadastro-header p {
  color: var(--neutral-text-light);
  font-size: var(--font-size-medium);
}

/* ===== FORMULÁRIO ===== */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  color: var(--neutral-text);
  font-weight: 500;
  font-size: 0.95rem;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"] {
  width: 100%;
  padding: 0.75rem var(--spacing-md);
  border: 2px solid var(--neutral-border);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  transition: all 0.3s ease;
  background: var(--neutral-light);
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(46, 38, 64, 0.1);
}

.form-group input::placeholder {
  color: #999;
}

/* ===== TIPO DE USUÁRIO ===== */
.tipo-usuario {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-sm);
  flex-wrap: wrap;
}

.tipo-usuario-option {
  flex: 1;
  min-width: 120px;
}

.tipo-usuario-option label {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem var(--spacing-md);
  border: 2px solid var(--neutral-border);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--neutral-light);
  font-weight: 500;
  text-align: center;
}

.tipo-usuario-option input[type="radio"] {
  margin-right: var(--spacing-xs);
  width: auto;
}

.tipo-usuario-option label:hover {
  border-color: var(--primary-color);
  background: rgba(46, 38, 64, 0.05);
}

.tipo-usuario-option input[type="radio"]:checked + label,
.tipo-usuario-option label:has(input[type="radio"]:checked) {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: var(--neutral-light);
}

/* Estilo específico para cada tipo de usuário */
.tipo-usuario-option.aluno label:has(input[type="radio"]:checked) {
  background: var(--student-color);
  border-color: var(--student-color);
}

.tipo-usuario-option.professor label:has(input[type="radio"]:checked) {
  background: var(--teacher-color);
  border-color: var(--teacher-color);
}

.tipo-usuario-option.admin label:has(input[type="radio"]:checked) {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
}

/* ===== BOTÃO PRINCIPAL ===== */
.btn-primary {
  width: 100%;
  padding: 0.8rem var(--spacing-md);
  background: linear-gradient(135deg, var(--success-color) 0%, var(--student-color) 100%);
  color: var(--neutral-light);
  border: none;
  border-radius: var(--border-radius);
  font-weight: bold;
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: var(--spacing-md);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #20a03a 0%, #2a15d4 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 161, 53, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
}

/* ===== LINK DE VOLTAR ===== */
.link-voltar {
  text-align: center;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid #eee;
}

.link-voltar p {
  color: var(--neutral-text-light);
  margin: 0;
}

.link-voltar a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.link-voltar a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* ===== MENSAGENS DE FEEDBACK ===== */
.message {
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  text-align: center;
  font-weight: 500;
}

.message.erro {
  background-color: #ffeaea;
  color: var(--secondary-color);
  border: 1px solid #ffcdd2;
}

.message.sucesso {
  background-color: #e8f5e8;
  color: var(--success-color);
  border: 1px solid #c8e6c9;
}

/* ===== RESPONSIVIDADE ===== */
@media (max-width: 768px) {
  .cadastro-container {
    flex-direction: column;
    max-width: 450px;
  }

  .cadastro-image {
    min-height: 200px;
    padding: var(--spacing-lg);
  }

  .cadastro-image h2 {
    font-size: 1.5rem;
  }

  .cadastro-image p {
    font-size: 1rem;
  }

  .cadastro-form {
    padding: var(--spacing-lg);
  }

  .cadastro-header h1 {
    font-size: 1.5rem;
  }

  .tipo-usuario {
    flex-direction: column;
  }

  .tipo-usuario-option {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  body {
    padding: var(--spacing-sm);
  }

  .cadastro-container {
    margin: 0;
  }

  .cadastro-image {
    min-height: 150px;
    padding: var(--spacing-md);
  }

  .cadastro-form {
    padding: var(--spacing-md);
  }
}