<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Home - Checkin Room</title>
  <link rel="stylesheet" href="/CSS/components/layout.css">
  <link rel="stylesheet" href="/CSS/homePage/styles.css">
</head>
<body>
  <div class="layout-container">
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
      <div class="sidebar-header">
        <h1 class="sidebar-logo">Checkin Room</h1>
      </div>
      <div class="sidebar-nav">
        <ul>
          <li>
            <a href="/reservar" class="nav-link">
              <span class="nav-icon">📅</span>
              <span class="nav-text">Reservas</span>
            </a>
          </li>
          <li>
            <a href="/reservas/<%= usuario.id %>/minhas" class="nav-link">
              <span class="nav-icon">📋</span>
              <span class="nav-text">Minhas Reservas</span>
            </a>
          </li>
          <% if (usuario.tipo === 'admin') { %>
          <li>
            <a href="/admin" class="nav-link">
              <span class="nav-icon">⚙️</span>
              <span class="nav-text">Painel Admin</span>
            </a>
          </li>
          <% } %>
        </ul>
      </div>
    </nav>

    <!-- Topbar -->
    <header class="topbar" id="topbar">
      <div class="topbar-left">
        <button class="sidebar-toggle" id="sidebarToggle">
          <span>☰</span>
        </button>
        <h1 class="page-title">Home</h1>
      </div>
      <div class="topbar-right">
        <a href="/logout" class="btn-logout">
          <span>🚪</span>
          <span>Sair</span>
        </a>
      </div>
    </header>

    <!-- Conteúdo Principal -->
    <main class="main-content" id="mainContent">
      <div class="home-container">
        <!-- Texto de Fundo -->
        <div class="background-text">CHECKIN ROOM</div>

        <!-- Conteúdo Principal -->
        <div class="home-content">
          <!-- Seção de Boas-vindas -->
          <section class="welcome-section">
            <h1 class="welcome-title">Sistema de Reservas</h1>
            <p class="welcome-subtitle">Inteli - Instituto de Tecnologia e Liderança</p>

            <div class="user-greeting">
              <h2>Bem-vindo, <span class="user-name"><%= usuario.nome %></span>!</h2>
            </div>
          </section>

          <!-- Seção de Informações -->
          <section class="info-section">
            <div class="info-card">
              <span class="icon">🏢</span>
              <h3>Salas Disponíveis</h3>
              <p>Acesse nossa variedade de salas equipadas para diferentes necessidades acadêmicas e profissionais.</p>
            </div>

            <div class="info-card">
              <span class="icon">⏰</span>
              <h3>Reservas Rápidas</h3>
              <p>Sistema intuitivo para agendar salas de forma rápida e eficiente, com confirmação em tempo real.</p>
            </div>

            <div class="info-card">
              <span class="icon">📊</span>
              <h3>Gestão Inteligente</h3>
              <p>Acompanhe suas reservas, histórico e estatísticas de uso através de um painel personalizado.</p>
            </div>
          </section>

          <!-- Seção de Estatísticas -->
          <section class="stats-section">
            <h3 class="stats-title">Estatísticas do Sistema</h3>
            <div class="stats-grid">
              <div class="stat-item">
                <span class="stat-number">15</span>
                <span class="stat-label">Salas Disponíveis</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">120</span>
                <span class="stat-label">Reservas Este Mês</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">85%</span>
                <span class="stat-label">Taxa de Ocupação</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">24/7</span>
                <span class="stat-label">Disponibilidade</span>
              </div>
            </div>
          </section>

          <!-- Seção Admin (se aplicável) -->
          <% if (usuario.tipo === 'admin') { %>
          <section class="admin-section">
            <h3>Acesso Administrativo</h3>
            <p>Como administrador, você tem acesso completo ao painel de gestão de reservas e usuários.</p>
            <a href="/admin" class="btn-admin">
              <span>⚙️</span>
              <span>Acessar Painel</span>
            </a>
          </section>
          <% } %>
        </div>
      </div>
    </main>
  </div>

  <script>
    // Funcionalidade da Sidebar
    const sidebar = document.getElementById('sidebar');
    const topbar = document.getElementById('topbar');
    const mainContent = document.getElementById('mainContent');
    const sidebarToggle = document.getElementById('sidebarToggle');

    sidebarToggle.addEventListener('click', () => {
      sidebar.classList.toggle('collapsed');
      topbar.classList.toggle('sidebar-collapsed');
      mainContent.classList.toggle('sidebar-collapsed');
    });

    // Marcar link ativo
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
      if (link.getAttribute('href') === currentPath ||
          (currentPath === '/home' && link.getAttribute('href') === '/')) {
        link.classList.add('active');
      }
    });
  </script>
</body>
</html>
